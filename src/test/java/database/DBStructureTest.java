package database;

import org.assertj.core.api.SoftAssertions;
import org.junit.jupiter.api.*;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.junit.jupiter.params.provider.ValueSource;
import utils.DBUtil;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assumptions.assumeTrue;
import static utils.DBUtil.*;

@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
class DBStructureTest {
    private static boolean allColumnsExists = false;

    @BeforeAll
    public static void beforeAll() throws SQLException, IOException {
        new DBUtil().getUtil().executeFile("init.sql");
    }

    @ParameterizedTest
    @ValueSource (strings = {"categories", "clubs", "children", "club_child"})
    void existTable(String tableName) throws SQLException {
        boolean actual = isTableExist(tableName);
        assertEquals(true, actual, "Table" + tableName + " should exists");
    }

    @Test
    @Order(1)
    void existColumn() throws SQLException {
        boolean actual = isColumnInTableExist("categories", "avatar");
        assertEquals(true, actual, "Column avatar in table categories doesn't exists");
        allColumnsExists = true;
    }

    @Order(2)
    @ParameterizedTest
    @CsvSource({"categories, avatar, character varying",
               "clubs, title, character varying",
               "children, first_name, character varying",
               "club_child, club_id, bigint",
               "club_child, child_id, bigint"
    })
    void columnType(String tableName, String columnName, String expectedType) throws SQLException {
        assumeTrue(allColumnsExists, "Skipping test for column type because the test for presence column failed.");

        String actual = getColumnType(tableName, columnName);
        assertEquals(expectedType, actual,
                String.format("Type for column %s in table %s should be %s", columnName, tableName, expectedType));
    }

    @ParameterizedTest
    @CsvSource({"categories, id",
                "clubs, id",
                "children, id"
    })
    void checkPK(String tableName, String expectedPk) throws SQLException {

        String actual = getPK(tableName);
        assertEquals(expectedPk, actual, String.format("PK for table %s should be named %s", tableName, expectedPk));
    }

    @ParameterizedTest
    @MethodSource ("fkTestData")
    void checkFKs(String tableName, List<String> expectedRefs) throws SQLException {
        List<String> actual = getFKs(tableName);
        SoftAssertions assertions = new SoftAssertions();
        expectedRefs.forEach(ref -> assertions.assertThat(actual.stream().anyMatch(e -> e.contains(ref)))
                .withFailMessage(String.format("Table %s should contain FK with %s", tableName, ref))
                .isTrue());
        assertions.assertAll();


/*        Instead of using Jupiter's assertAll for improved agility, it is advisable to utilize SoftAssertions from assertJ. So we should delete this code
        assertAll("Check if FK present",
                () -> assertTrue(actual.stream().anyMatch(e -> e.contains(expected.get(0))),
                        String.format("Table %s should contain FK with %s", tableName, expected.get(0))),
                () -> assertTrue(actual.stream().anyMatch(e -> e.contains(expected.get(1))),
                       String.format("Table %s should contain FK with %s", tableName, expected.get(1))));
*/
    }

    static Stream<Arguments> fkTestData() {
        return Stream.of(
                Arguments.of("club_child", List.of("club_id", "child_id")),
                Arguments.of("clubs", List.of("category_id")
        ));
    }

    @Test
    void checkNotNull() throws SQLException {
        List<String> actual = getNotNull("categories");
        assertTrue(actual.contains("title"), "Column title in table categories should be not null");
    }


    @ParameterizedTest
    @CsvSource({
            "clubs, title",
            "categories, title"
    })
    void checkUnique(String tableName, String columnName) throws SQLException {
        List<String> actual = getUnique(tableName);
        assertTrue(actual.contains(columnName),
                String.format("Column %s in table %s should be unique", columnName, tableName));
    }

}
